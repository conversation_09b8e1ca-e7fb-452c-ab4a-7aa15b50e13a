import json
import logging
import os
from datetime import datetime
from urllib.parse import urlparse

from dotenv import load_dotenv
from langchain.chains import create_history_aware_retriever, create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_mongodb import MongoDBAtlasVectorSearch
from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings

from app.core.config import get_ssm_parameters

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

chat_history = {}

# Get OpenAI parameters from SSM
params = get_ssm_parameters(
    ["openaikey", "embedding_azure_endpoint", "ai-model-endpoint", "central/dburl"]
)

# Get MongoDB URL from environment variables
db_url = os.getenv("LOCAL_DB_URL")
# Use the fallback value if db_url is not set
if not db_url:
    db_url = params.get("central/dburl")  # Use .get() to avoid KeyError
logger.info(f"Using database URL: {db_url}")

parsed_url = urlparse(db_url)
database_name = parsed_url.path.lstrip("/")

# Parse the embedding endpoint URL to get deployment and version
embedding_url = params["embedding_azure_endpoint"]
embedding_deployment = embedding_url.split("/deployments/")[1].split("/")[0]
embedding_version = embedding_url.split("api-version=")[1]

# Parse the AI model endpoint URL
ai_model_url = params["ai-model-endpoint"]
ai_model_deployment = ai_model_url.split("/deployments/")[1].split("/")[0]
ai_model_version = ai_model_url.split("api-version=")[1]

# Get Azure endpoint base URL
azure_base_url = "https://" + embedding_url.split("/")[2]

logger.info(
    f"Using embedding deployment: {embedding_deployment}, version: {embedding_version}"
)
logger.info(
    f"Using AI model deployment: {ai_model_deployment}, version: {ai_model_version}"
)

# Initialize Azure OpenAI embeddings using SSM parameters
embeddings = AzureOpenAIEmbeddings(
    azure_deployment=embedding_deployment,
    openai_api_version=embedding_version,
    openai_api_key=params["openaikey"],
    azure_endpoint=azure_base_url,
)

# Initialize Azure OpenAI LLM using SSM parameters
llm = AzureChatOpenAI(
    azure_deployment=ai_model_deployment,
    openai_api_version=ai_model_version,
    openai_api_key=params["openaikey"],
    azure_endpoint=azure_base_url,
    temperature=0.3,  # Lower temperature for more focused responses
)


custom_rag_prompt = """You are an expert assistant for SimplrOps, a powerful operations management platform. Your task is to provide accurate, helpful, and well-structured responses based on the provided documentation and never provide PII information in response.

        **Core Guidelines:**
        1. ACCURACY: Your primary goal is to provide accurate information. Only use information that is explicitly present in the provided passages.
        2. CLARITY: Structure responses clearly using HTML for optimal readability.
        3. COMPLETENESS: Address all aspects of the question comprehensively.
        4. RELEVANCE: Stay focused on the specific question asked.

        **Response Structure:**
        1. Start with a clear, direct answer to the main question
        2. Provide step-by-step instructions when applicable
        3. Include relevant URLs in the correct format
        4. Add helpful context without being verbose

        **HTML Formatting Rules:**
        - Use <p> for paragraphs
        - Use <strong> for important points and headings
        - Use <em> for emphasis
        - Use <ul> and <li> for lists
        - Format URLs as: <a href='url'>descriptive text</a>
        - Never use trailing slashes in URLs

        **URL Guidelines:**
        1. Always use the provided BASE_URL
        2. Format: <a href='BASE_URL/#/path'>descriptive text</a>
        3. Common patterns:
           - Main pages: BASE_URL/#/section-name
           - Sub-pages: BASE_URL/#/section/sub-section

        **Response Quality Checklist:**
        1. Is the information accurate and from the provided context?
        2. Is the HTML formatting correct and consistent?
        3. Are all URLs properly formatted?
        4. Is the response clear and well-structured?
        5. Have you avoided any confidentiality notices or references?
        6. Is the tone professional and helpful?

        Remember: You are the expert guide to SimplrOps. Focus on providing practical, actionable information that helps users accomplish their tasks efficiently.

        QUESTION: {input}
        BASE_URL: {url}
        PASSAGE: {context}

        **Reference Format Example:**
        <p><strong></strong></p>
        <p>The key point is <em>[Key Point]</em>. This means [Explanation]</p>
        <ul>
            <!-- For list items -->
            <li>
                <strong>Detail 1:</strong> [Detail Explanation]  
                <a href='url1'>URL for the description page</a> 
            </li>
            
            <li>
                <strong>Detail 2:</strong> [Detail Explanation] 
                <a href='url2'>URL for the described path</a> 
            </li>
            <!-- For URLs -->
            <a href='url1'>URL1</a>
            <a href='url2'>URL2</a>
        </ul>
        <p>If you need further clarification, please let me know!</p>

        **Example of Corrected Code:**
        <a href='https://dev-customer1.simplrops.com/#/home'>Home Page</a>
        <a href='https://dev-customer1.simplrops.com/#/digital-configuration-inventory/dataSetup'>Data Setup Page</a>

        **Note:** Don't put a `/` or `\` in the anchor tag.
        """

contextualize_q_system_prompt = (
    "Given a chat history and the latest user question "
    "which might reference context in the chat history, "
    "formulate a standalone question which can be understood "
    "without the chat history. Do NOT answer the question, "
    "just reformulate it if needed and otherwise return it as is."
)

# Initialize MongoDB vector search
data_retriever = MongoDBAtlasVectorSearch.from_connection_string(
    db_url,
    embedding=embeddings,
    namespace=f"{database_name}.SimplropsNewVectordata",
    index_name="vector_index",
)
retriever = data_retriever.as_retriever(
    search_type="similarity",
    search_kwargs={
        "k": 8,  # Increased from 5 to get more context
        "threshold": 0.70,  # Slightly lowered threshold for better recall
        "post_filter_pipeline": [],  # Add post-filtering capability if needed
    },
)


def simplrops_context(instructions, query, user_id, url):
    """
    SimplrOps context-aware question answering using LangChain.

    This function takes in a user query, user ID, and a URL as input and
    returns a response. It uses LangChain to create a contextualized question
    from the user's query and chat history, and then uses this contextualized
    question to search for relevant passages in the database. The passages are
    then used to generate a response using the LangChain RAG chain.

    The chat history for each user is maintained in-memory, and is used to
    contextualize the user's question. The chat history is limited to the last 6
    interactions for better context retention.

    Args:
        query (str): The user's question.
        user_id (str): The user's ID.
        url (str): The URL of the page the user is currently on.

    Returns:
        str: The response to the user's question.
    """

    try:
        logger.info(f"Processing query for user {user_id}: {query}")
        logger.info(f"Current URL context: {url}")

        # Clean up old chat histories to prevent memory leaks
        if len(chat_history) > 4:  # Limit total number of users
            logger.info(f"Clearing chat history. Current size: {len(chat_history)}")
            chat_history.clear()

        # Initialize chat history for the user if not already present
        if user_id not in chat_history:
            logger.info(f"Initializing chat history for user {user_id}")
            chat_history[user_id] = []

        # Clean query
        query = query.strip()
        logger.info(f"Cleaned query: {query}")

        # Define the prompt template for contextualizing questions
        contextualize_q_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", contextualize_q_system_prompt),
                MessagesPlaceholder("chat_history"),
                ("human", "{input}"),
            ]
        )

        # Create a history-aware retriever
        history_aware_retriever = create_history_aware_retriever(
            llm, retriever, contextualize_q_prompt
        )

        # Define the prompt template for question answering
        qa_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", instructions),
                MessagesPlaceholder("chat_history"),
                ("human", "{input}"),
            ]
        )

        # Create the question-answering chain
        question_answer_chain = create_stuff_documents_chain(llm, qa_prompt)

        # Create the retrieval-augmented generation (RAG) chain
        rag_chain = create_retrieval_chain(
            history_aware_retriever, question_answer_chain
        )

        # Log retrieval process
        logger.info("Starting RAG chain invocation")
        logger.info(f"Current chat history length: {len(chat_history[user_id])}")

        # Call the RAG chain with input query and chat history
        data = rag_chain.invoke(
            {"input": query, "chat_history": chat_history[user_id], "url": url}
        )

        # Log retrieved documents and answer
        if "source_documents" in data:
            logger.info(f"Retrieved {len(data['source_documents'])} documents")
            for i, doc in enumerate(data["source_documents"]):
                logger.info(f"Document {i+1} metadata: {json.dumps(doc.metadata)}")

        logger.info("Generated answer length: %d characters", len(data["answer"]))
        logger.debug(f"Generated answer: {data['answer']}")

        # Maintain only the last 6 interactions in chat history
        if len(chat_history[user_id]) > 6:
            logger.info("Trimming chat history to last 6 interactions")
            chat_history[user_id] = chat_history[user_id][-12:]

        # Add new interaction to chat history
        chat_history[user_id].extend(
            [HumanMessage(content=query), AIMessage(content=data["answer"])]
        )
        logger.info(f"Updated chat history length: {len(chat_history[user_id])}")

        return data["answer"]

    except KeyError as e:
        error_msg = f"KeyError: {e} - Ensure user_id exists and chat_history is initialized properly."
        logger.error(error_msg)
        raise
    except NameError as e:
        error_msg = f"NameError: {e} - Check if all variables like 'retriever', 'llm', etc., are defined."
        logger.error(error_msg)
        raise
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise
        print(f"An error occurred: {e}")
